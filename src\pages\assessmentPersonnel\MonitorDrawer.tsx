import React, { useState, useEffect } from 'react';
import { YTHForm, YTHDialog } from 'yth-ui';
import { queryUnitTreeData } from '@/service/baseModuleApi';
import { message, <PERSON><PERSON>, Spin } from 'antd';
import formApi from '@/service/formApi';
import { formatTree } from '@/utils/format';
import type { ActionType } from 'yth-ui/es/components/form/listFilter';
import moment from 'moment';
import type { Form } from '@formily/core/esm/models';
import type { ResponseType } from '@/service/envApi';
import {
  deleteEnvQualityMonitorById,
  insertEnvQualityDetail,
  queryEnvQualityDetail,
  updateEnvQualityDetail,
} from '@/service/envApi';
import LocationWithRadius from '@/components/common/locationWithRadius/locationWithRadius';
import TraceObjectSelector from '@/components/common/TraceObjectSelector';
import style from './baseInfo.module.less';

type PropsTypes = {
  type: string;
  dataObj: {
    id?: string;
    [key: string]: React.Key;
  };
  dictKey: string;
  showEquip?: boolean;
  closeModal: () => void;
};

type objectType = Record<string, string>;

/**
 * @description 查看 或新增 modal
 * @param param0
 * @returns
 */
const MonitorDrawer: React.FC<PropsTypes> = ({
  type, // 弹窗的类别 add 新增 view 查看 edit 编辑
  dataObj,
  /** 字典值 */
  dictKey,
  /** 公用工程 单独处理 */
  showEquip,
  closeModal = () => {},
}) => {
  const currentRef: React.Ref<
    | (ActionType & {
        delRow: (name: string, index: number) => void;
        addRows: (name: string, data: object[]) => void;
        copyRow: (name: string, index: number) => void;
      })
    | undefined
  > = React.useRef();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isCasRequested, setIsCasRequested] = useState<boolean>(false);
  const [radiusValue, setRadiusValue] = useState<number>(0); // 经纬度
  const form: Form = React.useMemo(() => YTHForm.createForm({}), []);

  // 查询详情
  const queryDataDetail: () => Promise<void> = async () => {
    setIsLoading(true);
    const res: ResponseType = await queryEnvQualityDetail({ id: dataObj?.id });
    if (res && res.code && res.code === 200) {
      const formD: Record<string, unknown> = res.data;
      formD.equipType = [
        {
          text: res.data.equipTypeText,
          code: res.data.equipType as string,
        },
      ];
      formD.facilityType = [
        {
          text: res.data.facilityTypeText,
          code: res.data.facilityType as string,
        },
      ];
      formD.monitorOnline = [
        {
          text: res.data.monitorOnlineText,
          code: res.data.monitorOnline as string,
        },
      ];
      formD.unitType = [
        {
          id: res.data?.companyId ?? '',
          code: res.data?.enterpriseId ?? '',
          name: res.data?.supplyUnit ?? '',
          key: res.data?.companyId ?? '',
          title: res.data?.supplyUnit ?? '',
        },
      ];
      formD.state = [
        {
          text: res.data.stateText ?? '',
          code: (res.data.state as string) || '',
        },
      ];
      setRadiusValue(res.data.radius as number);
      formD.coord =
        res.data.longitude && res.data.latitude ? `${res.data.longitude},${res.data.latitude}` : '';
      const newList: object[] = [];
      if (formD.list instanceof Array) {
        formD.list.forEach((item: objectType) => {
          // 处理溯源对象数据（从后端获取）
          // 如果后端返回的是字符串，需要解析为数组；如果没有数据，使用空数组
          let traceObjects: string[] = [];
          if (item.traceObjects) {
            try {
              // 如果是字符串，尝试解析为数组
              const parsed: string[] =
                typeof item.traceObjects === 'string'
                  ? JSON.parse(item.traceObjects)
                  : item.traceObjects;
              // 确保解析后的结果是数组
              traceObjects = Array.isArray(parsed) ? parsed : [];
            } catch {
              // 解析失败时使用空数组
              traceObjects = [];
            }
          }

          newList.push({
            ...item,
            key: item.id,
            indexCode: item.indexTypeCode,
            indexType: [{ code: item.indexTypeCode, text: item.indexTpNm }],
            frequencyCd: [{ code: item.frequency, text: item.frequencyText }],
            totalizeValue: [
              { code: item.isTotalizeValue, text: item?.isTotalizeValue === '1' ? '是' : '否' },
            ],
            trace: [{ code: item.isTraceable, text: item.isTraceableText }],
            traceObjects,
          });
        });
      }

      formD.list = newList;
      formD.monitorType = [
        { code: formD.monitorType as string, text: formD.monitorTypeText ?? '' },
      ];
      formD.useDate = moment(res.data.useDate as string).format('YYYY-MM-DD');
      form.setValues(formD);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (type && (type === 'edit' || type === 'view')) {
      queryDataDetail().then(() => {
        setIsCasRequested(true);
      });
    } else {
      setIsCasRequested(true);
    }
    return () => {
      setIsCasRequested(false);
    };
  }, [type, dataObj]);

  // 点击取消
  const cancel: () => void = () => {
    form.reset();
    closeModal();
  };

  // 新增保存
  const submitAddData: (data: Record<string, unknown>) => Promise<void> = async (data) => {
    setIsLoading(true);
    const res: ResponseType = await insertEnvQualityDetail(data);
    if (res && res.code && res.code === 200) {
      message.success('新增数据成功');
      closeModal();
    } else {
      message.error('新增数据失败');
    }
    setIsLoading(false);
  };

  // 编辑保存
  const submitEditData: (data: Record<string, unknown>) => Promise<void> = async (data) => {
    setIsLoading(true);
    const res: ResponseType = await updateEnvQualityDetail({ ...data, id: dataObj?.id });
    if (res && res.code && res.code === 200) {
      message.success('更新数据成功');
      closeModal();
    } else {
      message.error('更新数据失败');
    }
    setIsLoading(false);
  };

  // 确认删除数据
  const confirmDelete: (index: number, row: { id: string }) => Promise<void> = async (
    index,
    row,
  ) => {
    const res: ResponseType = await deleteEnvQualityMonitorById(row.id);
    if (res && res.code && res.code === 200) {
      message.success('删除数据成功');
      currentRef?.current.delRow('list', index + 1);
    } else {
      message.error('删除数据出错');
    }
  };

  // 点击保存
  const save: () => void = () => {
    form.validate().then(() => {
      let coordObj: string[] = [];
      if (form.values.coord && form.values.coord !== '' && form.values.coord.includes(',')) {
        coordObj = form.values.coord.split(',');
      }
      const listObj: objectType[] = JSON.parse(JSON.stringify(form.values.list));
      listObj.forEach((item: Record<string, string>, index: number) => {
        if (Array.isArray(item.indexType) && item.indexType.length > 0) {
          listObj[index].indexTypeCode = item.indexType[0]?.code ?? '';
          listObj[index].indexTpNm = item.indexType[0]?.text ?? '';
          listObj[index].indexTp = item.indexType[0]?.code ?? '';
          listObj[index].indexType = null;
        }
        // 处理溯源对象数据，保存到后端
        if (item.traceObjects) {
          // 将溯源对象数组转换为字符串格式
          listObj[index].traceObjects = Array.isArray(item.traceObjects)
            ? JSON.stringify(item.traceObjects)
            : item.traceObjects; // 如果已经是字符串则直接使用
        }
      });
      const submitData: Record<string, unknown> = {
        ...form.values,
        type: dictKey, // 监测设备类型
        equipType: form.values.equipType[0]?.code ?? '',
        monitorType: form.values.monitorType[0]?.code ?? '',
        monitorOnline: form.values.monitorOnline[0]?.code ?? '',
        companyId: form.values.unitType[0]?.id ?? '',
        supplyUnit: form.values.unitType[0]?.name ?? '',
        state: form.values.state[0].code,
        longitude: coordObj.length === 2 ? coordObj[0] : '',
        latitude: coordObj.length === 2 ? coordObj[1] : '',
        list: listObj,
        useDate: type === 'add' ? `${form.values.useDate} 00:00:00` : null,
      };
      if (showEquip) {
        submitData.facilityType = form.values.facilityType[0]?.code ?? '';
      }
      delete submitData.unitType;
      if (type === 'add') {
        submitAddData(submitData);
      } else if (type === 'edit') {
        submitEditData(submitData);
      }
    });
  };

  // 经纬度数据
  const locationConfirm: (v: { x: number; y: number; r: number }) => void = (v) => {
    setIsLoading(true);
    form.setValues({ coord: `${v.x},${v.y}`, radius: v.r });
    setIsLoading(false);
  };

  // 删除数据dialog
  const deleteTemplateDialog: (index: number, row: { id: string }) => void = (index, row) => {
    YTHDialog.show({
      type: 'confirm',
      content: <p>确认删除此条数据？</p>,
      onCancle: () => {},
      onConfirm: () => {
        confirmDelete(index, row);
      },
      p_props: {
        cancelText: '取消',
        okText: '确定',
        title: '删除',
      },
      m_props: {
        title: '删除',
      },
    });
  };

  return (
    <div>
      <Spin spinning={isLoading}>
        {isCasRequested && (
          <YTHForm form={form} col={2}>
            <YTHForm.Item
              name="id"
              title="id"
              labelType={1}
              required={false}
              display="hidden"
              componentName="Input"
              componentProps={{
                disabled: true,
              }}
            />

            <YTHForm.Item
              name="name"
              title="监测设备名称"
              labelType={1}
              required
              componentName="Input"
              componentProps={{
                disabled: type === 'view',
              }}
            />

            <YTHForm.Item
              name="code"
              title="设备编号"
              labelType={1}
              required
              componentName="Input"
              componentProps={{
                disabled: type === 'view',
              }}
            />

            <YTHForm.Item
              name="unitType"
              title="所属单位"
              labelType={1}
              required
              componentName="PickData"
              componentProps={{
                requestData: async () => {
                  try {
                    return formatTree(await queryUnitTreeData());
                  } catch {
                    return [];
                  }
                },
                multiple: false,
                disabled: type === 'view',
                p_props: {
                  placeholder: '请输入',
                },
              }}
            />

            <YTHForm.Item
              name="brand"
              title="品牌"
              labelType={1}
              componentName="Input"
              componentProps={{
                disabled: type === 'view',
              }}
            />

            <YTHForm.Item
              name="equipType"
              title="设备类型"
              labelType={1}
              required
              componentName="Selector"
              componentProps={{
                request: async () => {
                  const { list } = await formApi.getDictionary({
                    condition: {
                      fatherCode: 'A22A05',
                    },
                    currentPage: 0,
                    pageSize: 0,
                  });
                  const plOp: object[] = [];
                  list.forEach((item) => {
                    plOp.push({
                      text: item.text,
                      code: item.code,
                    });
                  });
                  return plOp;
                },
                disabled: type === 'view',
                p_props: {
                  changeOnSelect: true,
                  placeholder: '请输入',
                },
              }}
            />

            <YTHForm.Item
              name="monitorType"
              title="监测类型"
              labelType={1}
              required
              componentName="Selector"
              componentProps={{
                request: async () => {
                  const { list } = await formApi.getDictionary({
                    condition: {
                      fatherCode: dictKey === 'A22A08A03' ? 'A22A08A07' : dictKey,
                    },
                    currentPage: 0,
                    pageSize: 0,
                  });
                  const plOp: object[] = [];
                  list.forEach((item) => {
                    // 公用工程中的监测类型用到污染源中的污水字典项
                    if (dictKey === 'A22A08A03') {
                      if (item.code === 'A22A08A07A05') {
                        plOp.push({
                          code: item.code,
                          text: item.text,
                        });
                      }
                    } else if (dictKey === 'A22A08A07') {
                      if (item.code !== 'A22A08A07A05') {
                        plOp.push({
                          code: item.code,
                          text: item.text,
                        });
                      }
                    } else {
                      plOp.push({
                        code: item.code,
                        text: item.text,
                      });
                    }
                  });
                  return plOp;
                },
                disabled: type === 'view',
                p_props: {
                  changeOnSelect: true,
                  placeholder: '请输入',
                },
              }}
            />

            <YTHForm.Item
              name="monitorOnline"
              title="是否在线监测"
              labelType={1}
              required
              componentName="Selector"
              componentProps={{
                request: async () => {
                  const plOp: object[] = [
                    { code: '1', text: '是' },
                    { code: '0', text: '否' },
                  ];
                  return plOp;
                },
                disabled: type === 'view',
                p_props: {
                  changeOnSelect: true,
                  placeholder: '请输入',
                },
              }}
            />

            <YTHForm.Item
              name="description"
              title="监测对象"
              labelType={1}
              required
              componentName="Input"
              componentProps={{
                disabled: type === 'view',
              }}
            />

            <YTHForm.Item
              name="location"
              title="设备位置"
              labelType={1}
              mergeRow={2}
              required
              componentName="Input"
              componentProps={{
                disabled: type === 'view',
              }}
            />

            <YTHForm.Item
              name="coord"
              required
              mergeRow={2}
              title="经度和纬度"
              component={LocationWithRadius}
              componentProps={{
                disabled: true,
                radius: radiusValue ?? 0,
                operateType: type,
                confirmClick: locationConfirm,
              }}
            />

            <YTHForm.Item
              name="radius"
              title="覆盖半径"
              labelType={1}
              required={false}
              componentName="Input"
              componentProps={{
                disabled: type === 'view',
              }}
            />

            <YTHForm.Item
              name="height"
              title="高度"
              labelType={1}
              required={false}
              componentName="Input"
              componentProps={{
                disabled: type === 'view',
              }}
            />

            {showEquip && (
              <YTHForm.Item
                name="facilityType"
                title="监测设施类型"
                labelType={1}
                required
                componentName="Selector"
                componentProps={{
                  disabled: type === 'view',
                  request: async () => {
                    const { list } = await formApi.getDictionary({
                      condition: {
                        fatherCode: 'A22A07',
                      },
                      currentPage: 0,
                      pageSize: 0,
                    });
                    const plOp: object[] = [];
                    list.forEach((item) => {
                      plOp.push({
                        text: item.text,
                        code: item.code,
                      });
                    });
                    return plOp;
                  },
                }}
              />
            )}

            <YTHForm.Item
              name="ip"
              title="IP地址"
              labelType={1}
              required={false}
              componentName="Input"
              componentProps={{
                disabled: type === 'view',
              }}
            />

            <YTHForm.Item
              name="port"
              title="端口"
              labelType={1}
              required={false}
              componentName="Input"
              componentProps={{
                disabled: type === 'view',
              }}
            />

            <YTHForm.Item
              required
              title="投用日期"
              name="useDate"
              componentName="DatePicker"
              componentProps={{
                placeholder: '',
                precision: 'day',
                // range: true,
                formatter: 'YYYY-MM-DD',
                disabled: type === 'view' || type === 'edit',
                // onChange: (v) => {
                //   timePickerSelected();
                // },
              }}
            />
            <YTHForm.Item
              name="state"
              title="设备运行状态"
              labelType={1}
              required
              componentName="Selector"
              componentProps={{
                request: async () => {
                  const { list } = await formApi.getDictionary({
                    condition: {
                      fatherCode: 'A22A03',
                    },
                    currentPage: 0,
                    pageSize: 0,
                  });
                  const plOp: object[] = [];
                  list.forEach((item) => {
                    plOp.push({
                      text: item.text,
                      code: item.code,
                    });
                  });
                  return plOp;
                },

                disabled: type === 'view' || type === 'edit',
                p_props: {
                  changeOnSelect: true,
                  placeholder: '请输入',
                },
              }}
            />

            <YTHForm.List
              title="监测指标"
              columns={[
                {
                  title: '指标编码',
                  name: 'code',
                  minWidth: 80,
                  fixed: 'left',
                  edit: type !== 'view',
                  componentName: 'Input',

                  componentProps: {},
                  required: true,
                },
                {
                  title: '指标名称',
                  name: 'name',
                  minWidth: 80,
                  fixed: 'left',
                  edit: type !== 'view',
                  componentName: 'Input',

                  componentProps: {},
                  required: true,
                },

                // {
                //   title: '指标类型',
                //   name: 'indexType',
                //   minWidth: 120,
                //   edit: type !== 'view',
                //   componentName: 'Selector',
                //   componentProps: (record, rowIndex) => {
                //     return {
                //       request: async () => {
                //         const { list } = await formApi.getDictionary({
                //           condition: {
                //             fatherCode: 'A22A06',
                //           },
                //           currentPage: 0,
                //           pageSize: 0,
                //         });
                //         const plOp: object[] = [];
                //         list.forEach((item) => {
                //           plOp.push({
                //             text: item.text,
                //             code: item.code,
                //           });
                //         });
                //         return plOp;
                //       },
                //       onChange: (v: { code: string; text: string }[]) => {
                //         const tempLis: objectType[] = JSON.parse(JSON.stringify(form.values.list));
                //         tempLis.forEach((item, index) => {
                //           if (index === rowIndex) {
                //             tempLis[index].indexCode = v[0].code;
                //           }
                //         });
                //         form.setValues({
                //           list: tempLis,
                //         });
                //       },
                //       multiple: false,
                //       searchable: true,
                //     };
                //   },

                //   required: false,
                // },
                {
                  title: '是否溯源',
                  name: 'trace',
                  minWidth: 80,
                  edit: type !== 'view',
                  componentName: 'Selector',
                  // 只有环境空气质量监测有溯源
                  display: dictKey === 'A22A08A06',
                  componentProps: (record, rowIndex) => {
                    return {
                      request: async () => {
                        const { list } = await formApi.getDictionary({
                          condition: {
                            fatherCode: 'A03A01',
                          },
                          currentPage: 0,
                          pageSize: 0,
                        });
                        const plOp: object[] = [];
                        list.forEach((item) => {
                          plOp.push({
                            text: item.text,
                            code: item.code,
                          });
                        });
                        return plOp;
                      },
                      multiple: false,
                      onChange: (v: { code: string; text: string }[]) => {
                        const tempLis: objectType[] = JSON.parse(JSON.stringify(form.values.list));
                        tempLis.forEach((item, index) => {
                          if (index === rowIndex) {
                            tempLis[index].isTraceable = v[0].code;
                          }
                        });
                        form.setValues({
                          list: tempLis,
                        });
                      },
                    };
                  },
                  required: false,
                },
                {
                  title: '溯源对象个数',
                  name: 'traceObjects',
                  minWidth: 100,
                  edit: true, // 始终允许渲染组件，在组件内部控制查看/编辑行为
                  // 只有环境空气质量监测时显示溯源对象列
                  display: dictKey === 'A22A08A06',
                  component: TraceObjectSelector,
                  componentProps: (record, rowIndex) => {
                    return {
                      // 传入当前行的溯源对象数据（确保是数组）
                      value: Array.isArray(record?.traceObjects) ? record.traceObjects : [],
                      // 选择变化时更新表单数据
                      onChange: (value: string[]) => {
                        // 深拷贝表单数据避免直接修改原对象
                        const tempList: Record<string, unknown>[] = JSON.parse(
                          JSON.stringify(form.values.list || []),
                        );
                        if (tempList[rowIndex]) {
                          // 只更新溯源对象数组，不需要单独存储数量
                          tempList[rowIndex].traceObjects = value;
                        }
                        // 更新表单值
                        form.setValues({ list: tempList });
                      },
                      isTraceEnabled: true, // 传入溯源状态
                      type,
                    };
                  },
                },
                {
                  title: '采集频率',
                  name: 'frequencyCd',
                  minWidth: 120,
                  edit: type !== 'view',
                  componentName: 'Selector',
                  componentProps: (record, rowIndex) => {
                    return {
                      request: async () => {
                        const { list } = await formApi.getDictionary({
                          condition: {
                            fatherCode: 'A23A04',
                          },
                          currentPage: 0,
                          pageSize: 0,
                        });
                        const plOp: object[] = [];
                        list.forEach((item) => {
                          plOp.push({
                            text: item.text,
                            code: item.remark,
                          });
                        });
                        return plOp;
                      },
                      onChange: (v: { code: string; text: string }[]) => {
                        const tempLis: objectType[] = JSON.parse(JSON.stringify(form.values.list));
                        tempLis.forEach((item, index) => {
                          if (index === rowIndex) {
                            tempLis[index].frequency = v[0].code;
                          }
                        });
                        form.setValues({
                          list: tempLis,
                        });
                      },
                      multiple: false,
                      searchable: true,
                    };
                  },
                  required: true,
                },
                {
                  title: '是否累计值',
                  name: 'totalizeValue',
                  minWidth: 80,
                  edit: type !== 'view',
                  componentName: 'Selector',
                  componentProps: (record, rowIndex) => {
                    return {
                      request: () => {
                        return [
                          { text: '是', code: '1' },
                          { text: '否', code: '0' },
                        ];
                      },
                      multiple: false,
                      onChange: (v: { code: string; text: string }[]) => {
                        const tempLis: objectType[] = JSON.parse(JSON.stringify(form.values.list));
                        tempLis.forEach((item, index) => {
                          if (index === rowIndex) {
                            tempLis[index].isTotalizeValue = v[0].code;
                          }
                        });
                        form.setValues({
                          list: tempLis,
                        });
                      },
                    };
                  },
                  required: false,
                },
                // {
                //   title: '参数类型编码',
                //   name: 'indexCode',
                //   minWidth: 80,

                //   edit: false,
                //   componentName: 'Input',
                //   // componentProps: (record, rowIndex) => {
                //   //   return {
                //   //     onChange: (v) => {},
                //   //   };
                //   // },
                //   required: true,
                // },
                {
                  title: '计量单位',
                  name: 'measureUnit',
                  minWidth: 80,

                  edit: type !== 'view',
                  componentName: 'Input',
                  componentProps: {
                    defaultValue: 'ppm',
                  },
                  required: false,
                },
                {
                  title: '量程上限',
                  name: 'rangeMax',
                  minWidth: 80,

                  edit: type !== 'view',
                  componentName: 'Input',
                  componentProps: {
                    defaultValue: 'ppm',
                  },
                  required: false,
                },
                {
                  title: '量程下限',
                  name: 'rangeMin',
                  minWidth: 80,

                  edit: type !== 'view',
                  componentName: 'InputNumber',
                  componentProps: {
                    defaultValue: 'ppm',
                  },
                  required: false,
                },
                {
                  title: '一级阈值上限',
                  name: 'firstLevelMax',
                  minWidth: 80,

                  edit: type !== 'view',
                  componentName: 'InputNumber',
                  componentProps: {
                    defaultValue: 'ppm',
                  },
                  required: false,
                },
                {
                  title: '一级阈值下限',
                  name: 'firstLevelMin',
                  minWidth: 80,

                  edit: type !== 'view',
                  componentName: 'InputNumber',
                  componentProps: {
                    defaultValue: 'ppm',
                  },
                  required: false,
                },
                {
                  title: '二级阈值上限',
                  name: 'secondLevelMax',
                  minWidth: 80,

                  edit: type !== 'view',
                  componentName: 'InputNumber',
                  componentProps: {
                    defaultValue: 'ppm',
                  },
                  required: false,
                },
                {
                  title: '二级阈值下限',
                  name: 'secondLevelMin',
                  minWidth: 80,

                  edit: type !== 'view',
                  componentName: 'InputNumber',
                  componentProps: {
                    defaultValue: 'ppm',
                  },
                  required: false,
                },
              ]}
              name="list"
              rowOperations={[
                {
                  key: `list_key_${String(new Date())}`,
                  title: '删除',
                  type: 'danger',
                  disabled: () => type === 'view',
                  operation: (index, row: { id: string }) => {
                    if (row && row.id && row.id !== '') {
                      deleteTemplateDialog(index, row);
                    } else {
                      currentRef?.current.delRow('list', index + 1);
                    }
                  },
                },
                {
                  key: `list_key_${String(new Date())}`,
                  title: '复制',
                  type: 'primary',
                  disabled: () => type === 'view',
                  operation: (index) => {
                    currentRef?.current.copyRow('list', index + 1);
                  },
                },
              ]}
              extra={[
                {
                  key: 'addData',
                  title: '添加数据',
                  type: 'main',
                  disabled: type === 'view',
                  operation: () => {
                    // 添加新行，不需要手动初始化溯源对象字段
                    currentRef?.current.addRows('list', [{}]);
                  },
                },
              ]}
              actionRef={currentRef}
            />
          </YTHForm>
        )}
        <div className={style['drawer-filter-operation']}>
          {(type === 'add' || type === 'edit') && (
            <Button onClick={save} className={style['search-btn']} type="primary">
              保存
            </Button>
          )}
          <Button onClick={cancel} className={style['reset-btn']}>
            取消
          </Button>
        </div>
      </Spin>
    </div>
  );
};
export default MonitorDrawer;
