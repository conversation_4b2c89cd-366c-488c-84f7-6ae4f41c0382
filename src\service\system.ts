import { YTHToast } from 'yth-ui';
import requests, { rbacRequest } from '@/request';

export interface Unit {
  id?: string;
  parentId?: string;
  unitCode?: string;
  unitName?: string;
  shortName?: string;
  path?: string;
  unitType?: string;
  unitManager?: string;
  unitLeader?: string;
  description?: number;
  isGroup?: number;
  sortNo?: string;
  isEnable?: string;
  createBy?: string;
  createDate?: string;
  updateBy?: string;
  updateDate?: string;
  unitId?: string;
  orgId?: string;
  children?: Unit[];
}

export interface User {
  id?: string;
  userName?: string;
  userCode?: string;
  password?: string;
  realName?: string;
  phone?: string;
  avatar?: string;
  autograph?: string;
  sex?: string;
  email?: string;
  education?: string;
  hireDate?: string;
  birthday?: string;
  state?: string;
  identity?: string;
  type?: string;
  isLock?: string;
  isSystem?: string;
  isEnable?: string;
  sortNo?: number;
  description?: string;
  createBy?: string;
  createDate?: string;
  updateBy?: string;
  updateDate?: string;
  unitId?: string;
  orgId?: string;
  isDelete?: string;
  view?: boolean;
}

export interface Post {
  id?: string;
  postCode?: string;
  postName?: string;
  postType?: string;
  isEnable?: string;
  isSystem?: string;
  postDesc?: string;
  createBy?: string;
  createDate?: string;
  updateBy?: string;
  updateDate?: string;
  unitId?: string;
  orgId?: string;
}

export interface Role {
  id?: string;
  roleCode?: string;
  roleName?: string;
  roleType?: string;
  isEnable?: string;
  isSystem?: string;
  isPublic?: string;
  roleDesc?: string;
  createBy?: string;
  createDate?: string;
  updateBy?: string;
  updateDate?: string;
  unitId?: string;
  orgId?: string;
}

// 定义API响应的通用接口
interface ApiResponse<T> {
  data: T;
  code: number;
  msg: string;
}

export default {
  /**
   * @param url - API端点URL
   * @returns Promise<Unit | null>
   * @description 获取组织机构树形结构数据
   */
  unitTree: async (url: string = 'sys/unit/unitTree'): Promise<Unit | null> => {
    const resData: ApiResponse<Unit> = await rbacRequest<ApiResponse<Unit>>(url, {
      method: 'GET',
    });
    if (resData.code === 200) {
      return resData.data;
    }
    YTHToast.show({
      type: 'error',
      messageText: `服务器错误，${resData.msg}`,
      p_props: { duration: 10 },
      m_props: { duration: 3000 },
    });
    return null;
  },
  /**
   * @param url - API端点URL
   * @returns Promise<Unit | null>
   * @description 获取组织机构简单树形结构数据
   */
  simpleCompanyTree: async (url: string = 'sys/unit/simpleCompanyTree'): Promise<Unit | null> => {
    const resData: ApiResponse<Unit> = await requests<ApiResponse<Unit>>(url, {
      method: 'GET',
    });
    if (resData.code === 200) {
      return resData.data;
    }
    YTHToast.show({
      type: 'error',
      messageText: `服务器错误，${resData.msg}`,
      p_props: { duration: 10 },
      m_props: { duration: 3000 },
    });
    return null;
  },
  /**
   * @param url - API端点URL
   * @param unitId - 企业id
   * @returns Promise<User | null>
   * @description 获取用户列表
   */
  getUserList: async (
    url: string = '/sys/user/userList',
    unitId: string = '',
  ): Promise<User | null> => {
    const resData: ApiResponse<User> = await requests<ApiResponse<User>>(url, {
      method: 'GET',
      params: { unitId },
    });
    if (resData.code === 200) {
      return resData.data;
    }
    YTHToast.show({
      type: 'error',
      messageText: `服务器错误，${resData.msg}`,
      p_props: { duration: 10 },
      m_props: { duration: 3000 },
    });
    return null;
  },
  /**
   * @param url - API端点URL
   * @returns Promise<Role | null>
   * @description 获取角色列表
   */
  getUserRoles: async (url: string = '/sys/role/userRoles'): Promise<Role | null> => {
    const resData: ApiResponse<Role> = await requests<ApiResponse<Role>>(url, {
      method: 'GET',
    });
    if (resData.code === 200) {
      return resData.data;
    }
    YTHToast.show({
      type: 'error',
      messageText: `服务器错误，${resData.msg}`,
      p_props: { duration: 10 },
      m_props: { duration: 3000 },
    });
    return null;
  },
  /**
   * @param url - API端点URL
   * @returns Promise<Post | null>
   * @description 获取岗位列表
   */
  getPostList: async (url: string = '/sys/post/list'): Promise<Post | null> => {
    const resData: ApiResponse<Post> = await requests<ApiResponse<Post>>(url, {
      method: 'GET',
    });
    if (resData.code === 200) {
      return resData.data;
    }
    YTHToast.show({
      type: 'error',
      messageText: `服务器错误，${resData.msg}`,
      p_props: { duration: 10 },
      m_props: { duration: 3000 },
    });
    return null;
  },
};
