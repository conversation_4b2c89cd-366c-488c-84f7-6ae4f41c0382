import React, { useState, useEffect } from 'react';
import { YTHForm } from 'yth-ui';
import { message, Button, Spin, Space } from 'antd';
import moment from 'moment';
import assessmentApi from '@/service/assessmentApi';
import type {
  ApiResponse,
  AssessmentPersonnel,
  AssessmentPersonnelSaveParams,
} from '@/service/assessmentApi';
import type { Form } from '@formily/core/esm/models';

type PropsTypes = {
  dataObj?: AssessmentPersonnel;
  closeModal: () => void;
  mode: 'add' | 'edit' | 'view';
};

/**
 * @description 考核人员安排 新增、编辑、查看弹窗
 * @returns React.FC
 */
const AssessmentPersonnelModal: React.FC<PropsTypes> = ({
  dataObj,
  closeModal = () => {},
  mode = 'add',
}) => {
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const form: Form = React.useMemo(() => YTHForm.createForm({}), []);

  /**
   * 是否为只读模式
   */
  const isReadOnly: boolean = mode === 'view';

  useEffect(() => {
    if (dataObj && dataObj.id) {
      // 编辑或查看模式，设置表单值
      form.setValues({
        ...dataObj,
        learningTime: [dataObj.learnStartDate, dataObj.learnEndDate],
        examTime: [dataObj.examStartDate, dataObj.examEndDate],
      });
    } else {
      // 新增模式，设置默认值
      form.setValues({
        isExam: false, // 默认不考试
        learningTime: [
          moment().format('YYYY-MM-DD HH:mm:ss'),
          moment().add(7, 'days').format('YYYY-MM-DD HH:mm:ss'),
        ],
      });
    }
  }, [dataObj, form]);

  /**
   * 取消操作
   */
  const handleCancel: () => void = () => {
    form.reset();
    closeModal();
  };

  /**
   * 保存操作
   */
  const handleSave: () => Promise<void> = async () => {
    try {
      setIsSaving(true);

      // 表单验证
      await form.validate();
      const formValues: Record<string, unknown> = form.values;

      // 构造保存参数
      const saveParams: AssessmentPersonnelSaveParams = {
        id: dataObj?.id,
        planCode: formValues.planCode as string,
        companyId: formValues.companyId as string,
        planName: formValues.planName as string,
        trainingType: formValues.trainingType as string,
        trainingId: formValues.trainingId as string,
        trainingName: formValues.trainingName as string,
        learningDuration: formValues.learningDuration as string,
        learningObject: formValues.learningObject as string,
        learnStartDate: (formValues.learningTime as string[])?.[0],
        learnEndDate: (formValues.learningTime as string[])?.[1],
        trainingAttachmentPath: formValues.trainingAttachmentPath as string,
        trainingDate: formValues.trainingDate as string,
        trainingPlace: formValues.trainingPlace as string,
        lecturer: formValues.lecturer as string,
        isExam: formValues.isExam as boolean,
        examPaperId: formValues.examPaperId as string,
        examPaperName: formValues.examPaperName as string,
        examStartDate: (formValues.examTime as string[])?.[0],
        examEndDate: (formValues.examTime as string[])?.[1],
        passScore: formValues.passScore as number,
        examNumber: formValues.examNumber as number,
      };

      const result: ApiResponse<boolean> = await assessmentApi.saveAssessmentPersonnel(saveParams);

      if (result.code === 200) {
        message.success(mode === 'add' ? '新增成功' : '编辑成功');
        closeModal();
      } else {
        message.error(result.msg || '保存失败');
      }
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div>
      <Spin spinning={false}>
        <YTHForm form={form} col={2}>
          <YTHForm.Item
            name="planCode"
            title="计划编码"
            labelType={1}
            required
            componentName="Input"
            componentProps={{
              placeholder: '请输入计划编码',
              disabled: isReadOnly,
            }}
          />

          <YTHForm.Item
            name="planName"
            title="计划名称"
            labelType={1}
            required
            componentName="Input"
            componentProps={{
              placeholder: '请输入计划名称',
              disabled: isReadOnly,
            }}
          />

          <YTHForm.Item
            name="companyId"
            title="公司名称"
            labelType={1}
            required
            componentName="Input"
            componentProps={{
              placeholder: '请输入公司名称',
              disabled: isReadOnly,
            }}
          />

          <YTHForm.Item
            name="trainingType"
            title="培训类型"
            labelType={1}
            required
            componentName="Selector"
            componentProps={{
              request: async () => {
                return [
                  { code: '1', text: '技能培训' },
                  { code: '2', text: '安全培训' },
                  { code: '3', text: '综合培训' },
                  { code: '4', text: '专项培训' },
                ];
              },
              p_props: {
                placeholder: '请选择培训类型',
                disabled: isReadOnly,
              },
            }}
          />

          <YTHForm.Item
            name="trainingId"
            title="培训课程"
            labelType={1}
            required
            componentName="Input"
            componentProps={{
              placeholder: '请输入培训课程ID',
              disabled: isReadOnly,
            }}
          />

          <YTHForm.Item
            name="trainingName"
            title="培训课程名称"
            labelType={1}
            required
            componentName="Input"
            componentProps={{
              placeholder: '请输入培训课程名称',
              disabled: isReadOnly,
            }}
          />

          <YTHForm.Item
            name="learningDuration"
            title="学习时长"
            labelType={1}
            required
            componentName="Input"
            componentProps={{
              placeholder: '请输入学习时长',
              disabled: isReadOnly,
            }}
          />

          <YTHForm.Item
            name="learningObject"
            title="学习对象"
            labelType={1}
            required
            componentName="Input"
            componentProps={{
              placeholder: '请输入学习对象',
              disabled: isReadOnly,
            }}
          />

          <YTHForm.Item
            name="learningTime"
            title="学习时间"
            labelType={1}
            required
            componentName="DatePicker"
            componentProps={{
              precision: 'second',
              formatter: 'YYYY-MM-DD HH:mm:ss',
              placeholder: ['开始时间', '结束时间'],
              disabled: isReadOnly,
              mode: 'range',
            }}
          />

          <YTHForm.Item
            name="trainingPlace"
            title="培训地点"
            labelType={1}
            required
            componentName="Input"
            componentProps={{
              placeholder: '请输入培训地点',
              disabled: isReadOnly,
            }}
          />

          <YTHForm.Item
            name="lecturer"
            title="授课教师"
            labelType={1}
            required
            componentName="Input"
            componentProps={{
              placeholder: '请输入授课教师',
              disabled: isReadOnly,
            }}
          />

          <YTHForm.Item
            name="trainingDate"
            title="培训时间"
            labelType={1}
            required
            componentName="DatePicker"
            componentProps={{
              precision: 'second',
              formatter: 'YYYY-MM-DD HH:mm:ss',
              placeholder: '请选择培训时间',
              disabled: isReadOnly,
            }}
          />

          <YTHForm.Item
            name="isExam"
            title="是否考试"
            labelType={1}
            componentName="Switch"
            componentProps={{
              disabled: isReadOnly,
            }}
          />

          <YTHForm.Item
            name="examPaperName"
            title="考试试卷名称"
            labelType={1}
            componentName="Input"
            componentProps={{
              placeholder: '请输入考试试卷名称',
              disabled: isReadOnly,
            }}
          />

          <YTHForm.Item
            name="examTime"
            title="考试时间"
            labelType={1}
            componentName="DatePicker"
            componentProps={{
              precision: 'second',
              formatter: 'YYYY-MM-DD HH:mm:ss',
              placeholder: ['开始时间', '结束时间'],
              disabled: isReadOnly,
              mode: 'range',
            }}
          />

          <YTHForm.Item
            name="passScore"
            title="合格成绩"
            labelType={1}
            componentName="InputNumber"
            componentProps={{
              placeholder: '请输入合格成绩',
              disabled: isReadOnly,
              min: 0,
              max: 100,
            }}
          />

          <YTHForm.Item
            name="examNumber"
            title="补考次数"
            labelType={1}
            componentName="InputNumber"
            componentProps={{
              placeholder: '请输入补考次数',
              disabled: isReadOnly,
              min: 0,
            }}
          />

          <YTHForm.Item
            name="trainingAttachmentPath"
            title="培训课件"
            labelType={1}
            mergeRow={1}
            componentName="Input"
            componentProps={{
              placeholder: '请输入培训课件路径',
              disabled: isReadOnly,
            }}
          />
        </YTHForm>

        {/* 底部按钮 */}
        <div style={{ marginTop: '20px', textAlign: 'right' }}>
          <Space>
            <Button onClick={handleCancel}>{isReadOnly ? '关闭' : '取消'}</Button>
            {!isReadOnly && (
              <Button type="primary" loading={isSaving} onClick={handleSave}>
                保存
              </Button>
            )}
          </Space>
        </div>
      </Spin>
    </div>
  );
};

export default AssessmentPersonnelModal;
