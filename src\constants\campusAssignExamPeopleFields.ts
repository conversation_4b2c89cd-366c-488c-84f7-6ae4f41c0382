/**
 * 校园考试人员安排字段定义
 */
export interface CampusAssignExamPeopleField {
  code: string;
  name: string;
}

/**
 * 校园考试人员安排字段列表
 */
export const campusAssignExamPeopleFields: CampusAssignExamPeopleField[] = [
  { code: "id", name: "主键" },
  { code: "planCode", name: "计划编码" },
  { code: "companyId", name: "公司名称" },
  { code: "planName", name: "计划名称" },
  { code: "trainingType", name: "培训类型" },
  { code: "trainingId", name: "培训课程" },
  { code: "trainingName", name: "培训课程名称" },
  { code: "learningDuration", name: "学习时长" },
  { code: "learningObject", name: "学习对象" },
  { code: "learnStartDate", name: "学习开始时间" },
  { code: "learnEndDate", name: "学习结束时间" },
  { code: "trainingAttachmentPath", name: "培训课件" },
  { code: "trainingDate", name: "培训时间" },
  { code: "trainingPlace", name: "培训地点" },
  { code: "lecturer", name: "授课教师" },
  { code: "isExam", name: "是否考试" },
  { code: "examPaperId", name: "考试试卷" },
  { code: "examPaperName", name: "考试试卷名称" },
  { code: "examStartDate", name: "考试开始时间" },
  { code: "examEndDate", name: "考试结束时间" },
  { code: "passScore", name: "合格成绩" },
  { code: "examNumber", name: "补考次数" }
];
